﻿
using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
	[Authorize]
	[Route("api/[controller]")]
	[ApiController]
	public class CategoriesController : ControllerBase
	{

		private readonly ICategoryService _categoryService;

		public CategoriesController(ICategoryService categoryService)
		{
			_categoryService = categoryService;
		}
		[HttpGet("{projectKey}/categories")]
		public async Task<ActionResult<IEnumerable<Category>>> GetAllCategoriesByProjectKey([FromRoute] string projectKey)
		{
			var categories = await _categoryService.GetAllByProjectKeyAsync(projectKey);
			return Ok(categories);
		}

		[HttpGet("categories/{id}")]
		public async Task<ActionResult<Category>> GetCategoryById(int id)
		{
			var category = await _categoryService.GetByIdAsync(id);
			return Ok(category);
		}

		[HttpPost]
		public async Task<IActionResult> CreateCategory(CreateCategoryRequest request)
		{
			var result = await _categoryService.CreateAsync(request);

			if(!result.IsSuccess)
				return result.ToActionResult();

			return CreatedAtAction(nameof(GetCategoryById), new { id = result.Data!.Id }, request);
		}

		[HttpPut("categories/{id}")]
		public async Task<IActionResult> UpdateCategory(int id, [FromBody] UpdateCategoryRequest request)
		{
			var updateCategory = await _categoryService.UpdateAsync(id, request);
			if (!updateCategory)
				throw new Exception("Ko cap nhat duoc");

			return Ok();
		}

		[HttpDelete("categories/{id}")]
		public async Task<IActionResult> SoftDeleteCategory(int id)
		{
			var deleteCategory = await _categoryService.SoftDeleteAsync(id);
			if (!deleteCategory)
				throw new Exception("Ko cap nhat duoc");

			return NoContent();
		}

		[HttpPatch("categories/reoder")]
		public async Task<IActionResult> ReoderCategory([FromBody] List<int> ids)
		{
			var listCategories = await _categoryService.ReorderAsync(ids);
			if (!listCategories)
				throw new Exception("Ko cap nhat duoc");

			return Ok();
		}

	}
}
