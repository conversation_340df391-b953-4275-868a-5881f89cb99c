﻿using CleanArchitecture.API.DTOs.AppUser;
using CleanArchitecture.API.Extension;
using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.Auth;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {

        private readonly IAuthService _authService;
        public AuthController(IAuthService authService) 
        {
            _authService = authService;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterUserRequest request)
        {
            var result = await _authService.RegisterAsync(request);
			if (!result.IsSuccess)
				return result.ToActionResult();

			CookieHelper.SetAccessToken(Response, result.Data!.AccessToken);

            return result.ToActionResult(); ;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginUserRequest request)
        {
            var result = await _authService.LoginAsync(request.Email, request.Password);

            if (!result.IsSuccess)
				return result.ToActionResult();

			CookieHelper.SetAccessToken(Response, result.Data!.AccessToken);

			return Ok(new { message = "Đăng nhập thành công!", result.Data });
        }

    }

}
