﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IProjectRepository : IRepository<Project>
	{
		public Task<bool> IsNameDuplicated(string name);
		public Task<bool> IsProjectKeyDuplicated(string projectKey);
		public Task<int?> GetByKeyAsync(string projectKey);
	}
}
