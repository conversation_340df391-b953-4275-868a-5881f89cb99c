﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CleanArchitecture.API.DTOs.AppUser;
using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.Auth;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Shared.Helpers;
using Microsoft.IdentityModel.Tokens;

namespace CleanArchitectureAPI.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IAuthRepository _authRepository;

        public AuthService(IAuthRepository authRepository)
        {
            _authRepository = authRepository;
        }

        public Task<bool> ChangePassword(int id, string oldPassword, string newPassword)
        {
            throw new NotImplementedException();
        }

		public async Task<Result<AuthResultDto>> LoginAsync(string email, string password)
		{
			var appUser = await _authRepository.GetUserByEmailAsync(email);
            if (appUser == null)
                return Result<AuthResultDto>.Failure(ErrorCode.NotFound, "Email khong ton tai trong he thong");

			var isPasswordValid = BCrypt.Net.BCrypt.Verify(password, appUser.PasswordHash);
			if (!isPasswordValid)
				return Result<AuthResultDto>.Failure(ErrorCode.InvalidCredentials, "Mat khau khong chinh xac");

			var token = JwtHelper.GenerateToken(appUser, "your-very-secret-key-here-123456");

            return Result<AuthResultDto>.Success(new AuthResultDto {Email = appUser.Email, AccessToken = token, Id = appUser.Id});
		}


		public async Task<Result<AuthResultDto>> RegisterAsync(RegisterUserRequest registerUserDto)
        {
          
            if (await _authRepository.IsEmailExistsAsync(registerUserDto.Email))
				return Result<AuthResultDto>.Failure(ErrorCode.Conflict, "Email da ton tai");
			var passwordHash = PasswordHelper.HassPassword(registerUserDto.Password);

            AppUser appUser = new AppUser
            {
                Email = registerUserDto.Email,
                PasswordHash = passwordHash
            };

            await _authRepository.CreateAsync(appUser);

            var token = JwtHelper.GenerateToken(appUser , "your-very-secret-key-here-123456");
            return Result<AuthResultDto>.Success(new AuthResultDto { Email = registerUserDto.Email, AccessToken = token, Id = appUser.Id });
        }

    }
}

