﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.User;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class AppUserRepository : Repository<AppUser>, IAppUserRepository
	{
		public AppUserRepository(ApplicationDbContext context) : base(context)
		{
		}

		public AppUser Authenticate(string userName, string password)
		{
			throw new NotImplementedException();
		}

		public async Task<bool> IsEmailExistsAsync(string email)
		{
			return await _context.Set<AppUser>().AnyAsync(x => x.Email == email);
		}
	}
}
