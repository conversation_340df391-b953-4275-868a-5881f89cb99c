﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0-preview.6.*" />
		<PackageReference Include="Microsoft.SqlServer.Server" Version="1.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\CleanArchitectureAPI.Application\CleanArchitectureAPI.Application.csproj" />
		<ProjectReference Include="..\CleanArchitectureAPI.Domain\CleanArchitectureAPI.Domain.csproj" />
	</ItemGroup>

</Project>
