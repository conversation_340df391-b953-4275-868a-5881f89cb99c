﻿using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities
{
	public class Version : BaseProjectSetting
    {
		public Version(string name, string? description = null, int order = 0, string projectKey = "")
		{
			Name = name;
			Description = description;
			Order = order;
			ProjectKey = projectKey;
		}
		public Version() { }
	
		public DateTime? StartDate { get; set; } = null;
		public DateTime? EndDate { get; set; } = null;
		public bool IsReleased { get; set; } = false;
        public ICollection<IssueVersion> IssueVersions { get; set; } = new List<IssueVersion>();
    }
}
