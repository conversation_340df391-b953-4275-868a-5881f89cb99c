﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IProjectService
	{
		Task<Result<Project>> GetByIdAsync(int id);
		Task<IEnumerable<Result<Project>>> GetAllByProjectUserIdAsync(int id);
		Task<Result<Project>> CreateAsync(CreateProjectRequest dto);
		Task<Result<bool>> UpdateAsync(int id, UpdateProjectRequest dto);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> CheckProjectKeyDuplicate(string projectKey);
	}
}
