﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
	internal interface IStatusService 
	{
		Task<Category> GetByIdAsync(int id);
		Task<IEnumerable<Category>> GetAllByProjectKeyAsync(string projectKey);
		Task<Result<Category>> CreateAsync(CreateCategoryRequest dto);
		Task<bool> UpdateAsync(int id, UpdateCategoryRequest dto);
		Task<bool> SoftDeleteAsync(int id);
		Task<bool> ReorderAsync(List<int> orderedIds);
	}
}
