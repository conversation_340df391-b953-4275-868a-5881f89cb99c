﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class ChangeGroup:BaseProject
    {
        public int IssueId { get; set; }

        [ForeignKey(nameof(IssueId))]
        public required Issue Issue { get; set; }

        public int AuthorId { get; set; }

        [ForeignKey(nameof(AuthorId))]
        public required AppUser Author { get; set; }
    }
}
