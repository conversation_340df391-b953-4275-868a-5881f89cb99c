﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class CategoryRepository : Repository<Category>, ICategoryRepository
	{
		public CategoryRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Categories.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Categories
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		public async Task<IEnumerable<Category>> GetAllByProjectKeyAsync(string projectKey)
		{
			var categories = await _context.Categories.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).ToListAsync();

			if (categories == null || categories.Count == 0)
			{
				throw new KeyNotFoundException("Không có category nào tồn tại với projectKey đã cho.");
			}
			return categories;
		}


	}
}
