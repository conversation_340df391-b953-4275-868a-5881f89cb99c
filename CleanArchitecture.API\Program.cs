﻿
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using CleanArchitectureAPI.Infrastructure.Settings;
using CleanArchitectureAPI.Infrastructure.Auth;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using CleanArchitectureAPI.Domain.Interfaces.User;
using CleanArchitectureAPI.Application.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;
using CleanArchitectureAPI.Infrastructure.Repositories;
using FluentValidation.AspNetCore;
using CleanArchitectureAPI.Application.Requests.Auth;
using FluentValidation;
using CleanArchitecture.API.Middlewares;
var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Bind JwT
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("Jwt"));
builder.Services.AddSingleton(resolver =>
	resolver.GetRequiredService<IOptions<JwtSettings>>().Value);
// add service generate token 
builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();

// Database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
	options.UseSqlServer(
		builder.Configuration.GetConnectionString("DefaultConnection"),
		x => x.MigrationsAssembly("CleanArchitectureAPI.Infrastructure")
	));
// Dependency Injection
builder.Services.AddScoped<IAppUserServices, AppUserServices>();
builder.Services.AddScoped<IAppUserRepository, AppUserRepository>();
builder.Services.AddScoped<IAuthService, CleanArchitectureAPI.Application.Services.AuthService>();
builder.Services.AddScoped<IAuthRepository, AuthRepository>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<ICategoryService, CategoryServices>();
builder.Services.AddScoped<ICategoryRepository, CategoryRepository>();
builder.Services.AddScoped<IProjectService, ProjectService>();
builder.Services.AddScoped<IProjectRepository, ProjectRepository>();
builder.Services.AddScoped<IProjectAccessService, ProjectAccessService>();


builder.Services.AddHttpContextAccessor();
// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowLocalhost3000", policy =>
    {
        policy.WithOrigins("http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials(); // Cho phép cookie, session, token
    });
});

builder.Services.AddSwaggerGen(setup =>
{
	// Include 'SecurityScheme' to use JWT Authentication
	var jwtSecurityScheme = new OpenApiSecurityScheme
	{
		BearerFormat = "JWT",
		Name = "JWT Authentication",
		In = ParameterLocation.Header,
		Type = SecuritySchemeType.Http,
		Scheme = JwtBearerDefaults.AuthenticationScheme,
		Description = "Put **_ONLY_** your JWT Bearer token on textbox below!",

		Reference = new OpenApiReference
		{
			Id = JwtBearerDefaults.AuthenticationScheme,
			Type = ReferenceType.SecurityScheme
		}
	};

	setup.AddSecurityDefinition(jwtSecurityScheme.Reference.Id, jwtSecurityScheme);

	setup.AddSecurityRequirement(new OpenApiSecurityRequirement
	{
		{ jwtSecurityScheme, Array.Empty<string>() }
	});

});

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
	.AddJwtBearer(options =>
	{
		options.Events = new JwtBearerEvents
		{
			OnMessageReceived = context =>
			{
				// 1. Ưu tiên token từ header
				var authHeader = context.Request.Headers["Authorization"].ToString();
				if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
				{
					context.Token = authHeader.Substring("Bearer ".Length).Trim();
				}

				// 2. Nếu không có header thì lấy từ cookie
				else
				{
					var tokenFromCookie = context.Request.Cookies["accessToken"];
					if (!string.IsNullOrEmpty(tokenFromCookie))
					{
						context.Token = tokenFromCookie;
					}
				}
				return Task.CompletedTask;
			}
		};




		options.TokenValidationParameters = new TokenValidationParameters
		{
			ValidateIssuer = true,
			ValidateAudience = true,
			ValidateLifetime = true,
			ValidateIssuerSigningKey = true,
			ValidIssuer = "your-api",
			ValidAudience = "your-client",
			IssuerSigningKey = new SymmetricSecurityKey(
				Encoding.UTF8.GetBytes("your-very-secret-key-here-123456")
			)
		};
	});
	builder.Services.AddFluentValidationAutoValidation();
	builder.Services.AddFluentValidationClientsideAdapters();
	builder.Services.AddValidatorsFromAssemblyContaining<LoginUserRequestValidator>();


var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
	app.UseSwagger();
	app.UseSwaggerUI();
}


app.UseHttpsRedirection();
app.UseCors("AllowLocalhost3000");
app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();