﻿using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IAppUserServices
    {
        Task<IEnumerable<AppUser>> GetAllAppUsersAsync();
        Task<AppUser> GetAppUserByIdAsync(int id);
        Task<AppUser> CreateAppUserAsync(CreateAppUserDto createAppUserDto);
        //Task<AppUser> UpdateAppUserAsync(int id, UpdateAppUserDto updateAppUserDto);
        Task<bool> DeleteAppUserAsync(int id);
    }
}
