﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Services;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
	[Route("api/[controller]")]
	[Authorize]
	[ApiController]
	public class ProjectsController : ControllerBase
	{

		private readonly IProjectService _projectService;

		public ProjectsController(IProjectService projectService)
		{
			_projectService = projectService;
		}

		[HttpGet("project")]
		public async Task<ActionResult<IEnumerable<Project>>> GetAllProjectByUserId([FromQuery] int userId)
		{
			var projects = await _projectService.GetAllByProjectUserIdAsync(userId);
			return Ok(projects);
		}

		[HttpGet("project/{id}")]
		public async Task<ActionResult<Project>> GetProjectById(int id)
		{
			var project = await _projectService.GetByIdAsync(id);
			return Ok(project);
		}

		[HttpPost]
		public async Task<IActionResult> CreateProject(CreateProjectRequest request)
		{
			var result = await _projectService.CreateAsync(request);

			if (!result.IsSuccess)
				return result.ToActionResult();

			return CreatedAtAction(nameof(GetProjectById), new { id = result.Data!.Id }, result.Data);
		}

		[HttpPut("{id}")]
		public async Task<IActionResult> UpdateProject(int id, [FromBody] UpdateProjectRequest request)
		{	
		

			return Ok();
		}

		[HttpDelete("{id}")]
		public async Task<IActionResult> SoftDeleteProject(int id)
		{
			return NoContent();
		}

        [HttpGet("check-key/{projectKey}")]
        public async Task<IActionResult> CheckProjectKeyDuplicate(string projectKey)
        {
            var result = await _projectService.CheckProjectKeyDuplicate(projectKey);
            return result.ToActionResult(); // Mã hợp lệ, không trùng
        }

    }
}
