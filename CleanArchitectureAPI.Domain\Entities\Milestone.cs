﻿using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities
{
	public class Milestone : BaseProjectSetting
    {
		public Milestone(string name, string? description = null, int order = 0, string projectKey = "")
		{
			Name = name;
			Description = description;
			Order = order;
			ProjectKey = projectKey;
		}
		public Milestone() { }

		public DateTime? StartDate { get; set; } = null;
		public DateTime? EndDate { get; set; } = null;
		public bool IsReleased { get; set; } = false;
        public ICollection<IssueMilestone> IssueMilestones { get; set; } = new List<IssueMilestone>();
    }
}
