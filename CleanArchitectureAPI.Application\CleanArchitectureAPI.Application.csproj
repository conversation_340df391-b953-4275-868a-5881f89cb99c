﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CleanArchitecture.Shared\CleanArchitecture.Shared.csproj" />
    <ProjectReference Include="..\CleanArchitectureAPI.Domain\CleanArchitectureAPI.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Reponse\" />
    <Folder Include="Validators\Version\" />
  </ItemGroup>

</Project>
