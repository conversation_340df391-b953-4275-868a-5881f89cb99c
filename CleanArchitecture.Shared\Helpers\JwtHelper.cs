﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using CleanArchitectureAPI.Domain.Entities.User;
namespace CleanArchitectureAPI.Shared.Helpers
{
    public class JwtHelper
    {
        public static string GenerateToken(AppUser appUser, string secretKey, int expireMinutes = 60)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(secretKey);

            var tokenDescription = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[] {
        new Claim(ClaimTypes.NameIdentifier, appUser.Id.ToString()),
        new Claim(ClaimTypes.Email, appUser.Email),
    }),
                Expires = DateTime.UtcNow.AddMinutes(expireMinutes),
                Issuer = "your-api",            // 👈 thêm vào!
                Audience = "your-client",       // 👈 thêm vào!
                SigningCredentials = new SigningCredentials(
         new SymmetricSecurityKey(key),
         SecurityAlgorithms.HmacSha256Signature
     )
            };
            var token = tokenHandler.CreateToken(tokenDescription);
            return tokenHandler.WriteToken(token);

        }
    }
}


