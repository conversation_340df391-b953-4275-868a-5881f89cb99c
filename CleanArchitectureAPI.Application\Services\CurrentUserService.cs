﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Application.Interfaces;
using Microsoft.AspNetCore.Http;

namespace CleanArchitectureAPI.Application.Services
{
	public class CurrentUserService : ICurrentUserService
	{
		private readonly IHttpContextAccessor _httpContextAccessor;

		public CurrentUserService(IHttpContextAccessor httpContextAccessor)
		{
			_httpContextAccessor = httpContextAccessor;
		}

		public int GetUserId()
		{
			var claim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier);

            if (claim == null || !int.TryParse(claim.Value, out int userId))
				throw new UnauthorizedAccessException("User ID not found in token.");

			return userId;
		}


		public string GetUserName()
		{
			var name = _httpContextAccessor.HttpContext.User?.FindFirst(ClaimTypes.Email).Value;

            if (string.IsNullOrWhiteSpace(name))
				throw new UnauthorizedAccessException("User name not found in token.");
			return name;
		}

	}
}
