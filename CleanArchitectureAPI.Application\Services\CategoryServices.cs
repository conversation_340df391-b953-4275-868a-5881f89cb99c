﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;

namespace CleanArchitectureAPI.Application.Services
{
	public class CategoryServices : ICategoryService
	{
		private readonly ICategoryRepository _categoryRepository;
		private readonly ICurrentUserService _currentUserService;
		private readonly IProjectAccessService _projectAccessService;

		public CategoryServices(ICategoryRepository categoryRepository, ICurrentUserService currentUserService, IProjectAccessService projectAccessService)
		{

			_categoryRepository = categoryRepository;
			_currentUserService = currentUserService;
			_projectAccessService = projectAccessService;
		}

		public async Task<Result<Category>> CreateAsync(CreateCategoryRequest request)
		{

			int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);

			if (projectId is null)
				return Result<Category>.Failure(ErrorCode.NotFound, "Project không tồn tại");


			bool isDuplicate = await _categoryRepository.IsNameDuplicated(request.Name, request.ProjectKey);
			if (isDuplicate)
				return Result<Category>.Failure(ErrorCode.Conflict, "Ten danh muc da ton tai");

			// 3. Create entity
			Category category = new()
			{
				Name = request.Name.Trim(),
				ProjectId = projectId.Value,
				ProjectKey = request.ProjectKey,
				Description = request.Description?.Trim(),
				Color = "",
				IsDeleted = false,

				// 3.1 Set CreateAudit for entity 
				CreatedAt = DateTime.UtcNow,
				CreatedById = _currentUserService.GetUserId()
			};


			// 4. Set Order (optional)
			var currentMaxOrder = await _categoryRepository.GetMaxOrderInProjectAsync(request.ProjectKey);
			category.Order = currentMaxOrder + 1;

			// 5. Save
			await _categoryRepository.CreateAsync(category);

			// 6. Return
			return Result<Category>.Success(category);
		}

		public async Task<Category> GetByIdAsync(int id)
		{

			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
				throw new Exception($"Category with id {id} not found.");
			return category;

		}

		public async Task<IEnumerable<Category>> GetAllByProjectKeyAsync(string projectKey)
		{
			var categories = await _categoryRepository.GetAllByProjectKeyAsync(projectKey);

			return categories;
		}

		public async Task<bool> ReorderAsync(List<int> orderedIds)
		{

			var categories = await _categoryRepository.GetByIdsAsync(orderedIds);
			if (categories.Count() < 0)
				throw new ValidationException("Một số ID không tồn tại.");

			var sortedCategories = orderedIds
				.Select(id => categories.FirstOrDefault(c => c.Id == id))
				.Where(c => c != null)
				.ToList();
			for (int i = 0; i < sortedCategories.Count; i++)
			{
				sortedCategories[i].Order = i; // hoặc bất kỳ thuộc tính thứ tự nào
				await _categoryRepository.UpdateAsync(sortedCategories[i]);
			}
			return true;
		}

		public async Task<bool> SoftDeleteAsync(int id)
		{
			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
				throw new KeyNotFoundException($"Category with id {id} not found.");

			category.IsDeleted = true;
			return true;
		}

		public async Task<bool> UpdateAsync(int id, UpdateCategoryRequest request)
		{

			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
				throw new KeyNotFoundException($"Category with id {id} not found.");

			category.Name = request.Name;
			category.Description = request.Description;

			await _categoryRepository.UpdateAsync(category);
			return true;
		}
	}
}
