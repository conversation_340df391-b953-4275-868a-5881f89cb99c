﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class ChangeItem: BaseProject
    {
        public int GroupId { get; set; }

        [ForeignKey(nameof(GroupId))]
        public required ChangeGroup ChangeGroup { get; set; } 

        [Column(TypeName = "varchar(255)")]
        public required string FieldType { get; set; }

        [Column(TypeName = "varchar(255)")]
        public required string Field { get; set; }

        [Column(TypeName = "text")]
        public required string OldValue { get; set; }

        [Column(TypeName = "text")]
        public required string OldString { get; set; }

        [Column(TypeName = "text")]
        public required string NewValue { get; set; }

        [Column(TypeName = "text")]
        public required string NewString { get; set; }

    }
}
