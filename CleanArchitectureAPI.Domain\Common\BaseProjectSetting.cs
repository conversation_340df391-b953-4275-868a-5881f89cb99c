﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CleanArchitectureAPI.Domain.Common
{
	public class BaseProjectSetting : BaseProject
	{
		public required string Name { get; set; }
		public string? Description { get; set; }
		public  string Color { get; set; } = "#E0E0E0";
		public int Order { get; set; } = 0;
	}
}
