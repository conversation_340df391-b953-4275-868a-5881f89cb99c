﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.DTOs.AppUser;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Requests.Auth;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IAuthService 
	{
		Task<Result<AuthResultDto>> RegisterAsync(RegisterUserRequest registerUserDto);
		Task<Result<AuthResultDto>> LoginAsync (string username, string password);
		Task<bool> ChangePassword  (int id, string oldPassword, string newPassword);
	}
}
