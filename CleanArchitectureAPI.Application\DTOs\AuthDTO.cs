﻿namespace CleanArchitecture.API.DTOs.AppUser
{
	public class AuthDTO
	{
	}

	public class UserInfo
	{
		public int Id { get; set; }
		public string Name { get; set; }
		public string Email { get; set; }

	}
	public class AuthResultDto
	{
		public int Id { get; set; }
		public string AccessToken { get; set; }
		public string Email { get; set; }
	}


	public class ChangePasswordDto
	{
		public string OldPassword { get; set; }
		public string NewPassword { get; set; }
	}

	public class RegisterUserDto
	{
		public string Email { get; set; }
		public string Password { get; set; }
		public string Username { get; set; }
		public string ConfirmPassword { get; set; }
	}
}
