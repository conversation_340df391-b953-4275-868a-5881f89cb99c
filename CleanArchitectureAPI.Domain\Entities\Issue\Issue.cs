﻿
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.VisualBasic;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class Issue :  BaseProject
    {
        public required string ProjectIssueKey { get; set; }

        public required string Subject { get; set; }
        public string? Description { get; set; }

        public int? AssigneeId { get; set; }

        [ForeignKey(nameof(AssigneeId))]

        public AppUser? Assignee { get; set; }

        public required int ReporterId { get; set; }

        [ForeignKey(nameof(ReporterId))]
        public required AppUser Reporter { get; set; }
        public DateTime? StartDate { get; set; }

        public DateTime? DueDate { get; set; }

        public required int IssueTypeId { get; set; }

        [ForeignKey(nameof(IssueTypeId))]
        public required IssueType IssueType { get; set; }
     
        public required int StatusId { get; set; }

        [<PERSON><PERSON><PERSON>(nameof(StatusId))]
     
        public required Status Status { get; set; }

        public required int ResolutionId { get; set; }

        [ForeignKey(nameof(ResolutionId))]

        public required Resolution? Resolution { get; set; }

        public ICollection<IssueCategory>? IssueCategories { get; set; }

        public ICollection<IssueMilestone>? IssueMilestones { get; set; }

        public ICollection<IssueVersion>? IssueVersions { get; set; }

        public ICollection<Comment>? Comments { get; set; }
    }
}
